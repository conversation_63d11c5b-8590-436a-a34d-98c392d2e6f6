import discord
from discord.ext import commands
import asyncio
import os
from dotenv import load_dotenv

class TestCommands(commands.Cog):
    """Test commands for the Discord bot."""

    def __init__(self, bot):
        self.bot = bot

    @commands.command(name='test', help='Test if the bot is working')
    async def test(self, ctx):
        """Test if the bot is working."""
        await ctx.send("✅ Bot is working correctly!")

    @commands.command(name='testmusic', help='Test music playback functionality')
    async def testmusic(self, ctx):
        """Test music playback functionality."""
        # Check if bot is connected to voice
        if not ctx.voice_client:
            if ctx.author.voice:
                await ctx.author.voice.channel.connect()
                await ctx.send(f"Connected to {ctx.author.voice.channel.name}")
            else:
                await ctx.send("You need to be in a voice channel to use this command.")
                return

        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            await ctx.send("Music cog is not loaded.")
            return

        # Test a sample song
        await ctx.send("Testing music playback with a sample song...")
        await ctx.invoke(music_cog.play, query="https://www.youtube.com/watch?v=dQw4w9WgXcQ")

    @commands.command(name='testpanel', help='Test music panel functionality')
    async def testpanel(self, ctx):
        """Test music panel functionality."""
        # Get the music panel cog
        panel_cog = self.bot.get_cog('MusicPanel')
        if not panel_cog:
            await ctx.send("Music panel cog is not loaded.")
            return

        await ctx.send("Creating music control panel...")
        await ctx.invoke(panel_cog.panel)

    @commands.command(name='testfilters', help='Test audio filters')
    async def testfilters(self, ctx):
        """Test audio filters."""
        # Get the audio filters cog
        filters_cog = self.bot.get_cog('AudioFilters')
        if not filters_cog:
            await ctx.send("Audio filters cog is not loaded.")
            return

        await ctx.send("Displaying available audio filters...")
        await ctx.invoke(filters_cog.filter)

    @commands.command(name='testall', help='Run all tests')
    async def testall(self, ctx):
        """Run all tests."""
        await ctx.send("🧪 Starting comprehensive bot testing...")
        
        # Test basic functionality
        await ctx.invoke(self.test)
        
        # Test music playback
        await ctx.invoke(self.testmusic)
        
        # Wait for music to start
        await asyncio.sleep(5)
        
        # Test music panel
        await ctx.invoke(self.testpanel)
        
        # Test filters
        await ctx.invoke(self.testfilters)
        
        await ctx.send("✅ All tests completed!")

async def setup(bot):
    await bot.add_cog(TestCommands(bot))
