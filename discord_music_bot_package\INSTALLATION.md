# Discord Music Bot - Installation and Usage Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Commands](#commands)
5. [Music Panel](#music-panel)
6. [DJ Role](#dj-role)
7. [Audio Filters](#audio-filters)
8. [Troubleshooting](#troubleshooting)

## Introduction

This Discord Music Bot allows you to play music from YouTube, Spotify, and SoundCloud in your Discord server's voice channels. It features a comprehensive set of commands for music playback, queue management, playlists, and more, along with an interactive music panel interface.

### Features
- Play music from YouTube, Spotify, and SoundCloud
- Queue management system
- Playlist saving and loading
- Interactive music control panel
- DJ role management
- Audio filters
- Volume control

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)
- A Discord account and a server where you have admin permissions
- FFmpeg installed on your system

### Step 1: Create a Discord Bot
1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Go to the "Bot" tab and click "Add Bot"
4. Under the "Privileged Gateway Intents" section, enable:
   - Presence Intent
   - Server Members Intent
   - Message Content Intent
5. Copy your bot token (you'll need this later)

### Step 2: Invite the Bot to Your Server
1. Go to the "OAuth2" tab in the Discord Developer Portal
2. In the "URL Generator" section, select the following scopes:
   - bot
   - applications.commands
3. In the "Bot Permissions" section, select:
   - Send Messages
   - Embed Links
   - Attach Files
   - Read Message History
   - Add Reactions
   - Connect
   - Speak
   - Use Voice Activity
4. Copy the generated URL and open it in your browser
5. Select your server and authorize the bot

### Step 3: Install the Bot
1. Clone or download this repository to your computer
2. Navigate to the project directory
3. Create a virtual environment:
   ```
   python -m venv venv
   ```
4. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
5. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

### Step 4: Install FFmpeg
The bot requires FFmpeg to process audio. Install it according to your operating system:

- **Windows**:
  1. Download FFmpeg from [ffmpeg.org](https://ffmpeg.org/download.html)
  2. Extract the files to a folder (e.g., `C:\ffmpeg`)
  3. Add the `bin` folder to your system PATH

- **macOS**:
  ```
  brew install ffmpeg
  ```

- **Ubuntu/Debian**:
  ```
  sudo apt update
  sudo apt install ffmpeg
  ```

## Configuration

1. Rename the `.env.example` file to `.env`
2. Open the `.env` file and add your Discord bot token:
   ```
   DISCORD_TOKEN=your_discord_token_here
   COMMAND_PREFIX=-
   ```
3. If you want to use Spotify integration, add your Spotify API credentials:
   ```
   SPOTIFY_CLIENT_ID=your_spotify_client_id_here
   SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
   ```
   You can get these credentials by creating an app on the [Spotify Developer Dashboard](https://developer.spotify.com/dashboard/applications)

## Running the Bot

1. Make sure your virtual environment is activated
2. Run the bot:
   ```
   python src/bot.py
   ```
3. The bot should now be online in your Discord server

## Commands

All commands use the prefix `-` by default. You can change this in the `.env` file.

### General Commands
- `-help`: Shows the help message with all commands
- `-ping`: Checks the bot's latency

### Music Playback
- `-join`: Joins your current voice channel
- `-play <song>`: Plays a song from YouTube, Spotify, or SoundCloud
  - Can be a search query, URL, or playlist link
  - Examples:
    - `-play despacito`
    - `-play https://www.youtube.com/watch?v=dQw4w9WgXcQ`
    - `-play https://open.spotify.com/track/6habFhsOp2NvshLv26jgEs`
- `-pause`: Pauses the current song
- `-resume`: Resumes a paused song
- `-skip`: Skips the current song
- `-stop`: Stops playing and clears the queue
- `-leave`: Disconnects the bot from the voice channel

### Queue Management
- `-queue`: Shows the current song queue
- `-now`: Shows information about the currently playing song
- `-clear`: Clears the queue
- `-remove <number>`: Removes a specific song from the queue
- `-shuffle`: Shuffles the queue
- `-loop`: Toggles loop mode for the current song

### Volume Control
- `-volume <0-100>`: Sets the volume (0-100%)

### Playlists
- `-playlist save <name>`: Saves the current queue as a playlist
- `-playlist load <name>`: Loads a saved playlist
- `-playlist list`: Lists all saved playlists

### Music Panel
- `-panel`: Creates an interactive music control panel with buttons

### DJ Role
- `-djrole <role>`: Sets the DJ role for the server
- `-cleardjrole`: Clears the DJ role

### Audio Filters
- `-filter`: Shows available audio filters
- `-filter <name>`: Applies an audio filter to the current song
  - Available filters: bass, nightcore, vaporwave, 8d, clear

## Music Panel

The music panel provides an interactive interface for controlling music playback. To create a panel, use the `-panel` command.

The panel includes buttons for:
- Play/Pause
- Stop
- Skip
- Loop
- Shuffle
- Volume control

The panel automatically updates to show the currently playing song and queue.

## DJ Role

The DJ role system allows you to restrict certain music commands to users with a specific role.

To set up a DJ role:
1. Create a role in your Discord server (e.g., "DJ")
2. Use the `-djrole @DJ` command to set it as the DJ role
3. Assign the role to users who should have DJ permissions

To remove the DJ role, use the `-cleardjrole` command.

## Audio Filters

The bot includes several audio filters that can be applied to the currently playing music:

- `bass`: Enhances bass frequencies
- `nightcore`: Increases speed and pitch
- `vaporwave`: Decreases speed and pitch
- `8d`: Creates a rotating audio effect
- `clear`: Removes all applied filters

To apply a filter, use the `-filter <name>` command.

## Troubleshooting

### Bot doesn't respond to commands
- Make sure the bot is online
- Check that you're using the correct prefix
- Ensure the bot has the necessary permissions in your server

### Bot doesn't play music
- Make sure FFmpeg is installed correctly
- Check that the bot has permission to join and speak in voice channels
- Verify that the URL or search query is valid

### Spotify integration doesn't work
- Check that you've added valid Spotify API credentials to the `.env` file
- Make sure the Spotify API credentials have the correct permissions

### Bot disconnects unexpectedly
- This may be due to network issues
- The bot will automatically disconnect after 5 minutes of inactivity to save resources

## Support

If you encounter any issues or have questions, please create an issue on the GitHub repository or contact the bot developer.

---

Enjoy your Discord Music Bot! 🎵
