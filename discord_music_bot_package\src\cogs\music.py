import asyncio
import discord
from discord.ext import commands
from discord import app_commands
import youtube_dl
import spotipy
from spotipy.oauth2 import SpotifyClientCredentials
import os
import requests
import re
from async_timeout import timeout
import functools
import itertools
import math
import random

# Suppress noise about console usage from errors
youtube_dl.utils.bug_reports_message = lambda: ''

ytdl_format_options = {
    'format': 'bestaudio/best',
    'outtmpl': '%(extractor)s-%(id)s-%(title)s.%(ext)s',
    'restrictfilenames': True,
    'noplaylist': True,
    'nocheckcertificate': True,
    'ignoreerrors': False,
    'logtostderr': False,
    'quiet': True,
    'no_warnings': True,
    'default_search': 'auto',
    'source_address': '0.0.0.0',  # Bind to ipv4 since ipv6 addresses cause issues sometimes
}

ffmpeg_options = {
    'options': '-vn',
}

ytdl = youtube_dl.YoutubeDL(ytdl_format_options)

class YTDLSource(discord.PCMVolumeTransformer):
    def __init__(self, source, *, data, volume=0.5):
        super().__init__(source, volume)
        self.data = data
        self.title = data.get('title')
        self.url = data.get('url')
        self.duration = data.get('duration')
        self.thumbnail = data.get('thumbnail')
        self.uploader = data.get('uploader')
        self.uploader_url = data.get('uploader_url')
        self.description = data.get('description')
        self.source_type = 'youtube'

    @classmethod
    async def from_url(cls, url, *, loop=None, stream=False):
        loop = loop or asyncio.get_event_loop()
        data = await loop.run_in_executor(None, lambda: ytdl.extract_info(url, download=not stream))
        
        if 'entries' in data:
            # Take first item from a playlist
            data = data['entries'][0]
            
        filename = data['url'] if stream else ytdl.prepare_filename(data)
        return cls(discord.FFmpegPCMAudio(filename, **ffmpeg_options), data=data)

class MusicPlayer:
    """A class which is assigned to each guild using the bot for music playback.
    
    This class implements a queue and loop, which allows for different guilds to listen to different playlists
    simultaneously.
    
    When the bot disconnects from the voice it's instance will be destroyed.
    """

    __slots__ = ('bot', '_guild', '_channel', '_cog', 'queue', 'next', 'current', 'volume', 'loop', 'now_playing_message')

    def __init__(self, ctx):
        self.bot = ctx.bot
        self._guild = ctx.guild
        self._channel = ctx.channel
        self._cog = ctx.cog

        self.queue = asyncio.Queue()
        self.next = asyncio.Event()

        self.volume = 0.5
        self.current = None
        self.loop = False
        self.now_playing_message = None

        ctx.bot.loop.create_task(self.player_loop())

    async def player_loop(self):
        """Our main player loop."""
        await self.bot.wait_until_ready()

        while not self.bot.is_closed():
            self.next.clear()

            try:
                # Wait for the next song. If we timeout cancel the player and disconnect...
                async with timeout(300):  # 5 minutes...
                    source = await self.queue.get()
            except asyncio.TimeoutError:
                return self.destroy(self._guild)

            if not isinstance(source, YTDLSource):
                # Source was probably a stream (not downloaded)
                # So we should regather to prevent stream expiration
                try:
                    source = await YTDLSource.from_url(source.url, loop=self.bot.loop, stream=True)
                except Exception as e:
                    await self._channel.send(f'There was an error processing your song.\n'
                                             f'```css\n[{e}]\n```')
                    continue

            source.volume = self.volume
            self.current = source

            self._guild.voice_client.play(source, after=lambda _: self.bot.loop.call_soon_threadsafe(self.next.set))
            
            # Create and send the now playing embed
            embed = discord.Embed(title="Now Playing", description=f"[{source.title}]({source.url})", color=discord.Color.green())
            if source.thumbnail:
                embed.set_thumbnail(url=source.thumbnail)
            embed.add_field(name="Duration", value=self.parse_duration(source.duration) if source.duration else "Unknown")
            embed.add_field(name="Requested by", value=source.requester.mention if hasattr(source, 'requester') else "Unknown")
            
            self.now_playing_message = await self._channel.send(embed=embed)
            
            await self.next.wait()

            # Make sure the FFmpeg process is cleaned up.
            source.cleanup()
            self.current = None
            
            # If loop is enabled, add the song back to the queue
            if self.loop:
                await self.queue.put(source)

    def destroy(self, guild):
        """Disconnect and cleanup the player."""
        return self.bot.loop.create_task(self._cog.cleanup(guild))
    
    @staticmethod
    def parse_duration(duration):
        if duration is None:
            return "Unknown"
        minutes, seconds = divmod(duration, 60)
        hours, minutes = divmod(minutes, 60)
        
        if hours > 0:
            return f"{int(hours)}:{int(minutes):02d}:{int(seconds):02d}"
        else:
            return f"{int(minutes)}:{int(seconds):02d}"

class Music(commands.Cog):
    """Music commands for the Discord bot."""

    def __init__(self, bot):
        self.bot = bot
        self.players = {}
        
        # Initialize Spotify client if credentials are available
        spotify_client_id = os.getenv('SPOTIFY_CLIENT_ID')
        spotify_client_secret = os.getenv('SPOTIFY_CLIENT_SECRET')
        
        if spotify_client_id and spotify_client_secret:
            self.spotify = spotipy.Spotify(
                client_credentials_manager=SpotifyClientCredentials(
                    client_id=spotify_client_id,
                    client_secret=spotify_client_secret
                )
            )
        else:
            self.spotify = None

    async def get_player(self, ctx):
        """Retrieve the guild player, or generate one."""
        try:
            player = self.players[ctx.guild.id]
        except KeyError:
            player = MusicPlayer(ctx)
            self.players[ctx.guild.id] = player

        return player

    @commands.command(name='join', help='Joins a voice channel')
    async def join(self, ctx, *, channel: discord.VoiceChannel = None):
        """Join the voice channel the user is in."""
        if not channel and not ctx.author.voice:
            await ctx.send("You are not connected to a voice channel.")
            return False

        destination = channel or ctx.author.voice.channel
        
        if ctx.voice_client:
            await ctx.voice_client.move_to(destination)
        else:
            ctx.voice_client = await destination.connect()

        await ctx.send(f"Joined {destination.name}!")
        return True

    @commands.command(name='play', help='Plays a song from YouTube, Spotify, or SoundCloud')
    async def play(self, ctx, *, query):
        """Play a song from various sources."""
        # Join the voice channel if not already in one
        if not ctx.voice_client:
            success = await ctx.invoke(self.join)
            if not success:
                return

        async with ctx.typing():
            # Check if the query is a Spotify URL
            if 'spotify.com' in query and self.spotify:
                try:
                    # Handle different Spotify URL types
                    if 'track' in query:
                        # Single track
                        track_id = query.split('/')[-1].split('?')[0]
                        track = self.spotify.track(track_id)
                        search_query = f"{track['name']} {' '.join([artist['name'] for artist in track['artists']])}"
                        
                        # Search for the track on YouTube
                        source = await YTDLSource.from_url(f"ytsearch:{search_query}", loop=self.bot.loop, stream=True)
                        source.requester = ctx.author
                        
                        player = await self.get_player(ctx)
                        await player.queue.put(source)
                        await ctx.send(f"Added to queue: **{source.title}**")
                        
                    elif 'playlist' in query or 'album' in query:
                        # Playlist or album
                        await ctx.send("Processing Spotify playlist/album. This may take a moment...")
                        
                        if 'playlist' in query:
                            playlist_id = query.split('/')[-1].split('?')[0]
                            results = self.spotify.playlist_tracks(playlist_id)
                            tracks = results['items']
                        else:  # album
                            album_id = query.split('/')[-1].split('?')[0]
                            results = self.spotify.album_tracks(album_id)
                            tracks = results['items']
                        
                        # Limit to first 10 tracks to avoid rate limiting
                        track_count = min(10, len(tracks))
                        
                        player = await self.get_player(ctx)
                        added_tracks = 0
                        
                        for i, item in enumerate(tracks[:track_count]):
                            if 'playlist' in query:
                                track = item['track']
                            else:
                                track = item
                                
                            search_query = f"{track['name']} {' '.join([artist['name'] for artist in track['artists']])}"
                            
                            try:
                                source = await YTDLSource.from_url(f"ytsearch:{search_query}", loop=self.bot.loop, stream=True)
                                source.requester = ctx.author
                                await player.queue.put(source)
                                added_tracks += 1
                            except Exception as e:
                                print(f"Error adding track {search_query}: {e}")
                        
                        await ctx.send(f"Added {added_tracks} tracks to the queue from Spotify.")
                    
                except Exception as e:
                    await ctx.send(f"An error occurred processing Spotify link: {str(e)}")
                    # Fallback to YouTube search
                    await ctx.send("Falling back to YouTube search...")
                    source = await YTDLSource.from_url(f"ytsearch:{query}", loop=self.bot.loop, stream=True)
                    source.requester = ctx.author
                    
                    player = await self.get_player(ctx)
                    await player.queue.put(source)
                    await ctx.send(f"Added to queue: **{source.title}**")
            
            # Check if the query is a SoundCloud URL
            elif 'soundcloud.com' in query:
                try:
                    # Use youtube-dl to handle SoundCloud
                    source = await YTDLSource.from_url(query, loop=self.bot.loop, stream=True)
                    source.requester = ctx.author
                    source.source_type = 'soundcloud'
                    
                    player = await self.get_player(ctx)
                    await player.queue.put(source)
                    await ctx.send(f"Added to queue: **{source.title}**")
                    
                except Exception as e:
                    await ctx.send(f"An error occurred processing SoundCloud link: {str(e)}")
            
            # Default to YouTube
            else:
                try:
                    source = await YTDLSource.from_url(f"ytsearch:{query}" if not query.startswith(('https://', 'http://')) else query, 
                                                      loop=self.bot.loop, stream=True)
                    source.requester = ctx.author
                    
                    player = await self.get_player(ctx)
                    await player.queue.put(source)
                    await ctx.send(f"Added to queue: **{source.title}**")
                    
                except Exception as e:
                    await ctx.send(f"An error occurred: {str(e)}")

    @commands.command(name='pause', help='Pauses the currently playing song')
    async def pause(self, ctx):
        """Pause the currently playing song."""
        if ctx.voice_client and ctx.voice_client.is_playing():
            ctx.voice_client.pause()
            await ctx.send("Music paused ⏸️")
        else:
            await ctx.send("Nothing is playing right now.")

    @commands.command(name='resume', help='Resumes a paused song')
    async def resume(self, ctx):
        """Resume the currently paused song."""
        if ctx.voice_client and ctx.voice_client.is_paused():
            ctx.voice_client.resume()
            await ctx.send("Music resumed ▶️")
        else:
            await ctx.send("The music is not paused.")

    @commands.command(name='skip', help='Skips the current song')
    async def skip(self, ctx):
        """Skip the song."""
        if ctx.voice_client and (ctx.voice_client.is_playing() or ctx.voice_client.is_paused()):
            ctx.voice_client.stop()
            await ctx.send("Skipped the song ⏭️")
        else:
            await ctx.send("Nothing is playing right now.")

    @commands.command(name='queue', help='Shows the current song queue')
    async def queue_info(self, ctx):
        """Retrieve a basic queue of upcoming songs."""
        player = await self.get_player(ctx)
        
        if player.queue.empty() and not player.current:
            return await ctx.send('There are currently no songs in the queue.')

        # Get up to 10 entries from the queue
        upcoming = list(itertools.islice(player.queue._queue, 0, 10))
        
        # Create the embed
        embed = discord.Embed(title="Music Queue", color=discord.Color.blue())
        
        # Add current song
        if player.current:
            embed.add_field(
                name="Currently Playing",
                value=f"[{player.current.title}]({player.current.url})",
                inline=False
            )
        
        # Add upcoming songs
        if upcoming:
            queue_list = "\n".join(f"`{i+1}.` [{t.title}]({t.url})" for i, t in enumerate(upcoming))
            embed.add_field(name="Up Next", value=queue_list, inline=False)
            
            # Add queue length info
            if len(player.queue._queue) > 10:
                embed.set_footer(text=f"And {len(player.queue._queue) - 10} more songs in queue")
        
        await ctx.send(embed=embed)

    @commands.command(name='now', help='Shows the currently playing song')
    async def now_playing(self, ctx):
        """Display information about the currently playing song."""
        player = await self.get_player(ctx)
        
        if not player.current:
            return await ctx.send('Nothing is playing right now.')
        
        source = player.current
        
        # Create the embed
        embed = discord.Embed(title="Now Playing", description=f"[{source.title}]({source.url})", color=discord.Color.green())
        
        if source.thumbnail:
            embed.set_thumbnail(url=source.thumbnail)
            
        embed.add_field(name="Duration", value=player.parse_duration(source.duration) if source.duration else "Unknown")
        embed.add_field(name="Requested by", value=source.requester.mention if hasattr(source, 'requester') else "Unknown")
        
        if source.uploader:
            embed.add_field(name="Uploader", value=f"[{source.uploader}]({source.uploader_url})" if source.uploader_url else source.uploader)
        
        await ctx.send(embed=embed)

    @commands.command(name='volume', help='Changes the player volume')
    async def volume(self, ctx, volume: int):
        """Change the player volume."""
        if ctx.voice_client is None:
            return await ctx.send("Not connected to a voice channel.")

        if 0 > volume > 100:
            return await ctx.send("Volume must be between 0 and 100.")

        player = await self.get_player(ctx)
        
        ctx.voice_client.source.volume = volume / 100
        player.volume = volume / 100
        await ctx.send(f"Changed volume to {volume}%")

    @commands.command(name='stop', help='Stops playing music and clears the queue')
    async def stop(self, ctx):
        """Stop the currently playing song and clear the queue."""
        player = await self.get_player(ctx)
        
        # Clear the queue
        while not player.queue.empty():
            await player.queue.get()
            
        if ctx.voice_client and (ctx.voice_client.is_playing() or ctx.voice_client.is_paused()):
            ctx.voice_client.stop()
            
        await ctx.send("Music stopped and queue cleared ⏹️")

    @commands.command(name='leave', help='Clears the queue and leaves the voice channel')
    async def leave(self, ctx):
        """Stop the player and leave the voice channel."""
        if ctx.voice_client:
            await self.cleanup(ctx.guild)
            await ctx.send("Disconnected from voice channel 👋")
        else:
            await ctx.send("I'm not connected to a voice channel.")

    @commands.command(name='loop', help='Toggles loop mode')
    async def loop(self, ctx):
        """Toggle loop mode."""
        player = await self.get_player(ctx)
        player.loop = not player.loop
        
        await ctx.send(f"Loop mode is now {'enabled' if player.loop else 'disabled'} 🔁")

    @commands.command(name='shuffle', help='Shuffles the queue')
    async def shuffle(self, ctx):
        """Shuffle the queue."""
        player = await self.get_player(ctx)
        
        if player.queue.empty():
            return await ctx.send('The queue is empty. Add some songs first.')
            
        # Get all items from the queue
        queue_list = list(player.queue._queue)
        random.shuffle(queue_list)
        
        # Clear the queue and add shuffled items back
        player.queue._queue.clear()
        for item in queue_list:
            await player.queue.put(item)
            
        await ctx.send("Queue shuffled 🔀")

    @commands.command(name='remove', help='Removes a song from the queue')
    async def remove(self, ctx, index: int):
        """Remove a specific song from the queue by index."""
        player = await self.get_player(ctx)
        
        if player.queue.empty():
            return await ctx.send('The queue is empty.')
            
        if index < 1 or index > len(player.queue._queue):
            return await ctx.send(f'Index must be between 1 and {len(player.queue._queue)}.')
            
        # Get all items from the queue
        queue_list = list(player.queue._queue)
        removed_song = queue_list.pop(index - 1)
        
        # Clear the queue and add remaining items back
        player.queue._queue.clear()
        for item in queue_list:
            await player.queue.put(item)
            
        await ctx.send(f"Removed **{removed_song.title}** from the queue.")

    @commands.command(name='clear', help='Clears the queue')
    async def clear(self, ctx):
        """Clear the queue."""
        player = await self.get_player(ctx)
        
        if player.queue.empty():
            return await ctx.send('The queue is already empty.')
            
        # Clear the queue
        while not player.queue.empty():
            await player.queue.get()
            
        await ctx.send("Queue cleared 🧹")

    @commands.command(name='playlist', help='Saves or loads a playlist')
    async def playlist(self, ctx, action: str, name: str = None):
        """Save or load a playlist."""
        if action.lower() not in ['save', 'load', 'list']:
            return await ctx.send("Invalid action. Use 'save', 'load', or 'list'.")
            
        if action.lower() in ['save', 'load'] and not name:
            return await ctx.send("Please provide a playlist name.")
            
        # Create playlists directory if it doesn't exist
        os.makedirs(os.path.join(os.getcwd(), 'playlists'), exist_ok=True)
        
        if action.lower() == 'save':
            player = await self.get_player(ctx)
            
            if player.queue.empty() and not player.current:
                return await ctx.send('There are no songs to save.')
                
            playlist_data = []
            
            # Add current song if playing
            if player.current:
                playlist_data.append({
                    'title': player.current.title,
                    'url': player.current.url
                })
                
            # Add queued songs
            for song in list(player.queue._queue):
                playlist_data.append({
                    'title': song.title,
                    'url': song.url
                })
                
            # Save to file
            import json
            with open(os.path.join(os.getcwd(), 'playlists', f'{name}.json'), 'w') as f:
                json.dump(playlist_data, f)
                
            await ctx.send(f"Playlist '{name}' saved with {len(playlist_data)} songs.")
            
        elif action.lower() == 'load':
            import json
            playlist_path = os.path.join(os.getcwd(), 'playlists', f'{name}.json')
            
            if not os.path.exists(playlist_path):
                return await ctx.send(f"Playlist '{name}' not found.")
                
            with open(playlist_path, 'r') as f:
                playlist_data = json.load(f)
                
            if not playlist_data:
                return await ctx.send(f"Playlist '{name}' is empty.")
                
            # Join voice channel if not already
            if not ctx.voice_client:
                success = await ctx.invoke(self.join)
                if not success:
                    return
                    
            player = await self.get_player(ctx)
            
            await ctx.send(f"Loading {len(playlist_data)} songs from playlist '{name}'...")
            
            for i, song in enumerate(playlist_data):
                if i >= 10:  # Limit to 10 songs to avoid rate limiting
                    await ctx.send(f"Added first 10 songs. {len(playlist_data) - 10} more songs were not added to avoid rate limiting.")
                    break
                    
                try:
                    source = await YTDLSource.from_url(song['url'], loop=self.bot.loop, stream=True)
                    source.requester = ctx.author
                    await player.queue.put(source)
                except Exception as e:
                    print(f"Error loading song {song['title']}: {e}")
                    
            await ctx.send(f"Playlist '{name}' loaded successfully.")
            
        elif action.lower() == 'list':
            import glob
            playlists = glob.glob(os.path.join(os.getcwd(), 'playlists', '*.json'))
            
            if not playlists:
                return await ctx.send("No playlists found.")
                
            playlist_names = [os.path.basename(p).replace('.json', '') for p in playlists]
            await ctx.send(f"Available playlists: {', '.join(playlist_names)}")

    async def cleanup(self, guild):
        """Cleanup the guild player."""
        try:
            await guild.voice_client.disconnect()
        except AttributeError:
            pass

        try:
            del self.players[guild.id]
        except KeyError:
            pass

    @play.before_invoke
    async def ensure_voice(self, ctx):
        if ctx.voice_client is None:
            if ctx.author.voice:
                await ctx.author.voice.channel.connect()
            else:
                await ctx.send("You are not connected to a voice channel.")
                raise commands.CommandError("Author not connected to a voice channel.")

async def setup(bot):
    await bot.add_cog(Music(bot))
