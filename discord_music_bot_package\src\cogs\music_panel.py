import discord
from discord.ext import commands
import asyncio
from discord.ui import View, Button, Select
import datetime

class MusicPanel(commands.Cog):
    """Music panel interface for the Discord bot."""

    def __init__(self, bot):
        self.bot = bot
        self.panels = {}

    @commands.command(name='panel', help='Creates an interactive music control panel')
    async def panel(self, ctx):
        """Create an interactive music control panel."""
        # Create embed for the music panel
        embed = discord.Embed(
            title="Music Control Panel",
            description="Use the buttons below to control the music playback",
            color=discord.Color.purple()
        )
        
        embed.add_field(name="Now Playing", value="Nothing is playing", inline=False)
        embed.add_field(name="Queue", value="The queue is empty", inline=False)
        embed.set_footer(text=f"Requested by {ctx.author.display_name}", 
                         icon_url=ctx.author.display_avatar.url)
        embed.timestamp = datetime.datetime.now()
        
        # Create buttons for the panel
        view = self.create_panel_view(ctx)
        
        # Send the panel
        panel_message = await ctx.send(embed=embed, view=view)
        
        # Store the panel for updates
        self.panels[ctx.guild.id] = {
            'message': panel_message,
            'channel': ctx.channel
        }
        
        # Schedule regular updates
        self.bot.loop.create_task(self.update_panel(ctx.guild.id))
        
    def create_panel_view(self, ctx):
        """Create the button view for the music panel."""
        view = View(timeout=None)  # No timeout for persistent panel
        
        # Play/Pause button
        play_pause = Button(style=discord.ButtonStyle.primary, emoji="⏯️", row=0)
        play_pause.callback = lambda interaction: self.play_pause_callback(interaction, ctx)
        view.add_item(play_pause)
        
        # Stop button
        stop = Button(style=discord.ButtonStyle.danger, emoji="⏹️", row=0)
        stop.callback = lambda interaction: self.stop_callback(interaction, ctx)
        view.add_item(stop)
        
        # Skip button
        skip = Button(style=discord.ButtonStyle.primary, emoji="⏭️", row=0)
        skip.callback = lambda interaction: self.skip_callback(interaction, ctx)
        view.add_item(skip)
        
        # Loop button
        loop = Button(style=discord.ButtonStyle.secondary, emoji="🔁", row=0)
        loop.callback = lambda interaction: self.loop_callback(interaction, ctx)
        view.add_item(loop)
        
        # Shuffle button
        shuffle = Button(style=discord.ButtonStyle.secondary, emoji="🔀", row=0)
        shuffle.callback = lambda interaction: self.shuffle_callback(interaction, ctx)
        view.add_item(shuffle)
        
        # Volume control
        volume_options = [
            discord.SelectOption(label="10%", value="10"),
            discord.SelectOption(label="25%", value="25"),
            discord.SelectOption(label="50%", value="50"),
            discord.SelectOption(label="75%", value="75"),
            discord.SelectOption(label="100%", value="100")
        ]
        volume_select = Select(placeholder="Volume Control", options=volume_options, row=1)
        volume_select.callback = lambda interaction: self.volume_callback(interaction, ctx)
        view.add_item(volume_select)
        
        return view
        
    async def play_pause_callback(self, interaction, ctx):
        """Handle play/pause button press."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        # Check if something is playing
        if ctx.voice_client and ctx.voice_client.is_playing():
            await ctx.invoke(music_cog.pause)
        elif ctx.voice_client and ctx.voice_client.is_paused():
            await ctx.invoke(music_cog.resume)
        else:
            await interaction.followup.send("Nothing is playing right now. Use the play command to add songs.", ephemeral=True)
            
    async def stop_callback(self, interaction, ctx):
        """Handle stop button press."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        await ctx.invoke(music_cog.stop)
        
    async def skip_callback(self, interaction, ctx):
        """Handle skip button press."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        await ctx.invoke(music_cog.skip)
        
    async def loop_callback(self, interaction, ctx):
        """Handle loop button press."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        await ctx.invoke(music_cog.loop)
        
    async def shuffle_callback(self, interaction, ctx):
        """Handle shuffle button press."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        await ctx.invoke(music_cog.shuffle)
        
    async def volume_callback(self, interaction, ctx):
        """Handle volume selection."""
        await interaction.response.defer()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return
            
        # Get selected volume
        volume = int(interaction.data['values'][0])
        await ctx.invoke(music_cog.volume, volume=volume)
        
    async def update_panel(self, guild_id):
        """Update the music panel periodically."""
        await self.bot.wait_until_ready()
        
        while not self.bot.is_closed():
            try:
                if guild_id not in self.panels:
                    break
                    
                panel = self.panels[guild_id]
                message = panel['message']
                channel = panel['channel']
                
                # Get the music cog
                music_cog = self.bot.get_cog('Music')
                if not music_cog:
                    await asyncio.sleep(5)
                    continue
                    
                # Get the guild
                guild = self.bot.get_guild(guild_id)
                if not guild:
                    break
                    
                # Get the player
                try:
                    player = music_cog.players[guild_id]
                except KeyError:
                    # No player active
                    embed = message.embeds[0]
                    embed.set_field_at(0, name="Now Playing", value="Nothing is playing", inline=False)
                    embed.set_field_at(1, name="Queue", value="The queue is empty", inline=False)
                    embed.timestamp = datetime.datetime.now()
                    
                    await message.edit(embed=embed)
                    await asyncio.sleep(5)
                    continue
                    
                # Update the embed
                embed = message.embeds[0]
                
                # Update now playing
                if player.current:
                    now_playing = f"[{player.current.title}]({player.current.url})"
                    embed.set_field_at(0, name="Now Playing", value=now_playing, inline=False)
                else:
                    embed.set_field_at(0, name="Now Playing", value="Nothing is playing", inline=False)
                    
                # Update queue
                if player.queue.empty():
                    embed.set_field_at(1, name="Queue", value="The queue is empty", inline=False)
                else:
                    # Get up to 5 entries from the queue
                    upcoming = list(player.queue._queue)[:5]
                    queue_text = "\n".join(f"`{i+1}.` {t.title}" for i, t in enumerate(upcoming))
                    
                    if len(player.queue._queue) > 5:
                        queue_text += f"\n\n*+{len(player.queue._queue) - 5} more songs in queue*"
                        
                    embed.set_field_at(1, name=f"Queue ({len(player.queue._queue)} songs)", value=queue_text, inline=False)
                    
                embed.timestamp = datetime.datetime.now()
                
                await message.edit(embed=embed)
                
            except Exception as e:
                print(f"Error updating music panel: {e}")
                
            await asyncio.sleep(5)  # Update every 5 seconds

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle bot disconnection."""
        if member.id == self.bot.user.id and after.channel is None:
            # Bot was disconnected from voice
            guild_id = before.channel.guild.id
            
            if guild_id in self.panels:
                panel = self.panels[guild_id]
                message = panel['message']
                
                # Update the embed
                embed = message.embeds[0]
                embed.set_field_at(0, name="Now Playing", value="Nothing is playing", inline=False)
                embed.set_field_at(1, name="Queue", value="The queue is empty", inline=False)
                embed.timestamp = datetime.datetime.now()
                
                await message.edit(embed=embed)

async def setup(bot):
    await bot.add_cog(MusicPanel(bot))
