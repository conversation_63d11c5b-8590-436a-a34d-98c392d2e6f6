import discord
from discord.ext import commands
import asyncio
from discord.ext.commands import has_permissions

class <PERSON><PERSON><PERSON>(commands.Cog):
    """DJ role management for the Discord bot."""

    def __init__(self, bot):
        self.bot = bot
        self.dj_roles = {}  # Guild ID -> DJ role ID

    @commands.command(name='djrole', help='Sets or shows the DJ role')
    @has_permissions(manage_roles=True)
    async def djrole(self, ctx, role: discord.Role = None):
        """Set or show the DJ role for the server."""
        if role is None:
            # Show current DJ role
            if ctx.guild.id in self.dj_roles:
                dj_role = ctx.guild.get_role(self.dj_roles[ctx.guild.id])
                if dj_role:
                    await ctx.send(f"The current DJ role is: {dj_role.mention}")
                else:
                    await ctx.send("The DJ role is not set or no longer exists.")
            else:
                await ctx.send("No DJ role has been set for this server.")
        else:
            # Set DJ role
            self.dj_roles[ctx.guild.id] = role.id
            await ctx.send(f"DJ role set to: {role.mention}")

    @commands.command(name='cleardjrole', help='Clears the DJ role')
    @has_permissions(manage_roles=True)
    async def cleardjrole(self, ctx):
        """Clear the DJ role for the server."""
        if ctx.guild.id in self.dj_roles:
            del self.dj_roles[ctx.guild.id]
            await ctx.send("DJ role has been cleared.")
        else:
            await ctx.send("No DJ role was set.")

    def is_dj(self, member):
        """Check if a member has the DJ role."""
        if member.guild.id not in self.dj_roles:
            return False
            
        dj_role_id = self.dj_roles[member.guild.id]
        return any(role.id == dj_role_id for role in member.roles)

    async def cog_check(self, ctx):
        """Check if the user can use DJ commands."""
        # Commands in this cog can only be used by server admins
        return True

    @commands.Cog.listener()
    async def on_command_error(self, ctx, error):
        """Handle command errors."""
        if isinstance(error, commands.CheckFailure):
            await ctx.send("You don't have permission to use this command.")

async def setup(bot):
    await bot.add_cog(DJRole(bot))
