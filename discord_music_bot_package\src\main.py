import os
import discord
from discord.ext import commands
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')
PREFIX = os.getenv('COMMAND_PREFIX', '-')

# Set up intents
intents = discord.Intents.default()
intents.message_content = True
intents.voice_states = True
intents.guilds = True

# Initialize bot
bot = commands.Bot(command_prefix=PREFIX, intents=intents)

# Load cogs
async def load_extensions():
    for filename in os.listdir('./src/cogs'):
        if filename.endswith('.py'):
            await bot.load_extension(f'src.cogs.{filename[:-3]}')
            print(f'Loaded extension: {filename[:-3]}')

@bot.event
async def on_ready():
    print(f'{bot.user.name} has connected to Discord!')
    print(f'Bot is in {len(bot.guilds)} guilds')
    await bot.change_presence(activity=discord.Activity(
        type=discord.ActivityType.listening, 
        name=f"{PREFIX}help"
    ))

@bot.event
async def setup_hook():
    await load_extensions()

# Basic commands
@bot.command(name='ping', help='Check bot latency')
async def ping(ctx):
    await ctx.send(f'Pong! Latency: {round(bot.latency * 1000)}ms')

# Run the bot
if __name__ == "__main__":
    bot.run(TOKEN)
