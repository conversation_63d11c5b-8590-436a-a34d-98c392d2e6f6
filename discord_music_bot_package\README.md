# Discord Music Bot

A feature-rich Discord music bot that supports YouTube, Spotify, and SoundCloud with advanced features like music panels, DJ roles, and audio filters.

## Features

- Play music from YouTube, Spotify, and SoundCloud
- Queue management system
- Interactive music control panel
- Playlist saving and loading
- DJ role management
- Audio filters
- Volume control

## Requirements

- Python 3.8 or higher
- FFmpeg
- Discord Bot Token
- Spotify API credentials (optional)

## Quick Start

1. Clone this repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure your `.env` file with your Discord token
4. Run the bot: `python src/bot.py`

For detailed installation and usage instructions, see [INSTALLATION.md](INSTALLATION.md).

## Command Prefix

The default command prefix is `-`. You can change this in the `.env` file.

## Basic Commands

- `-play <song>`: Play a song or add it to the queue
- `-pause`: Pause the current song
- `-resume`: Resume playback
- `-skip`: Skip to the next song
- `-queue`: View the current queue
- `-panel`: Create an interactive music control panel

For a complete list of commands, use `-help` or see the documentation.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
