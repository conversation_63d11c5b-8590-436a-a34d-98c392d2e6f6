import os
import sys
import discord
from discord.ext import commands
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')
PREFIX = os.getenv('COMMAND_PREFIX', '-')

# Set up intents
intents = discord.Intents.default()
intents.message_content = True
intents.voice_states = True
intents.guilds = True

# Initialize bot
bot = commands.Bot(command_prefix=PREFIX, intents=intents)

# Load cogs
async def load_extensions():
    for filename in os.listdir('./src/cogs'):
        if filename.endswith('.py'):
            try:
                await bot.load_extension(f'src.cogs.{filename[:-3]}')
                print(f'Loaded extension: {filename[:-3]}')
            except Exception as e:
                print(f'Failed to load extension {filename}: {e}')

@bot.event
async def on_ready():
    print(f'{bot.user.name} has connected to Discord!')
    print(f'Bot is in {len(bot.guilds)} guilds')
    await bot.change_presence(activity=discord.Activity(
        type=discord.ActivityType.listening, 
        name=f"{PREFIX}help"
    ))

@bot.event
async def setup_hook():
    await load_extensions()

# Basic commands
@bot.command(name='ping', help='Check bot latency')
async def ping(ctx):
    await ctx.send(f'Pong! Latency: {round(bot.latency * 1000)}ms')

# Error handling
@bot.event
async def on_command_error(ctx, error):
    if isinstance(error, commands.CommandNotFound):
        await ctx.send(f"Command not found. Use `{PREFIX}help` to see available commands.")
    elif isinstance(error, commands.MissingRequiredArgument):
        await ctx.send(f"Missing required argument. Use `{PREFIX}help {ctx.command}` for proper usage.")
    elif isinstance(error, commands.BadArgument):
        await ctx.send(f"Invalid argument provided. Use `{PREFIX}help {ctx.command}` for proper usage.")
    else:
        await ctx.send(f"An error occurred: {str(error)}")
        print(f"Command error: {error}")

# Run the bot
if __name__ == "__main__":
    if TOKEN is None:
        print("Error: No Discord token provided. Please set the DISCORD_TOKEN environment variable.")
        sys.exit(1)
    
    print("Starting Discord Music Bot...")
    bot.run(TOKEN)
