import discord
from discord.ext import commands
import asyncio

class AudioFilters(commands.Cog):
    """Audio filter commands for the Discord bot."""

    def __init__(self, bot):
        self.bot = bot
        self.filters = {}  # Guild ID -> active filters

    @commands.command(name='filter', help='Apply an audio filter to the music')
    async def filter(self, ctx, filter_type: str = None):
        """Apply an audio filter to the currently playing music."""
        if not ctx.voice_client or not (ctx.voice_client.is_playing() or ctx.voice_client.is_paused()):
            return await ctx.send("Nothing is playing right now.")
            
        if filter_type is None:
            # Show available filters
            filters_list = ["bass", "nightcore", "vaporwave", "8d", "clear"]
            active_filter = self.filters.get(ctx.guild.id, "None")
            
            embed = discord.Embed(
                title="Audio Filters",
                description="Available filters:",
                color=discord.Color.blue()
            )
            
            for f in filters_list:
                if f == active_filter:
                    embed.add_field(name=f"{f} (Active)", value="✅", inline=True)
                else:
                    embed.add_field(name=f, value="❌", inline=True)
                    
            embed.set_footer(text=f"Use {ctx.prefix}filter <name> to apply a filter")
            return await ctx.send(embed=embed)
            
        filter_type = filter_type.lower()
        
        # Get the music cog
        music_cog = self.bot.get_cog('Music')
        if not music_cog:
            return await ctx.send("Music system is not available.")
            
        # Get the player
        try:
            player = music_cog.players[ctx.guild.id]
        except KeyError:
            return await ctx.send("No active music player found.")
            
        # Apply the filter
        if filter_type == "clear":
            # Clear all filters
            self.filters[ctx.guild.id] = None
            
            # Reset the FFmpeg options
            if player.current:
                # We need to restart the current song with default options
                current_source = player.current
                ctx.voice_client.stop()
                
                # Re-create the source with default options
                try:
                    source = await music_cog.YTDLSource.from_url(
                        current_source.url, 
                        loop=self.bot.loop, 
                        stream=True
                    )
                    source.volume = player.volume
                    
                    # Play the source
                    ctx.voice_client.play(
                        source, 
                        after=lambda _: self.bot.loop.call_soon_threadsafe(player.next.set)
                    )
                    
                    await ctx.send("Cleared all audio filters.")
                except Exception as e:
                    await ctx.send(f"An error occurred: {str(e)}")
                    
        elif filter_type in ["bass", "nightcore", "vaporwave", "8d"]:
            self.filters[ctx.guild.id] = filter_type
            
            # Apply the filter
            if player.current:
                # We need to restart the current song with the filter
                current_source = player.current
                ctx.voice_client.stop()
                
                # Set FFmpeg options based on the filter
                ffmpeg_options = {
                    'options': '-vn '
                }
                
                if filter_type == "bass":
                    ffmpeg_options['options'] += '-af "bass=g=10"'
                elif filter_type == "nightcore":
                    ffmpeg_options['options'] += '-af "asetrate=48000*1.25,aresample=48000,atempo=0.8"'
                elif filter_type == "vaporwave":
                    ffmpeg_options['options'] += '-af "asetrate=48000*0.8,aresample=48000,atempo=1.1"'
                elif filter_type == "8d":
                    ffmpeg_options['options'] += '-af "apulsator=hz=0.09"'
                
                # Re-create the source with the filter
                try:
                    source = await music_cog.YTDLSource.from_url(
                        current_source.url, 
                        loop=self.bot.loop, 
                        stream=True,
                        ffmpeg_options=ffmpeg_options
                    )
                    source.volume = player.volume
                    
                    # Play the source
                    ctx.voice_client.play(
                        source, 
                        after=lambda _: self.bot.loop.call_soon_threadsafe(player.next.set)
                    )
                    
                    await ctx.send(f"Applied {filter_type} filter.")
                except Exception as e:
                    await ctx.send(f"An error occurred: {str(e)}")
        else:
            await ctx.send(f"Unknown filter: {filter_type}. Use `{ctx.prefix}filter` to see available filters.")

async def setup(bot):
    await bot.add_cog(AudioFilters(bot))
